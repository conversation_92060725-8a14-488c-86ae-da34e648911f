import 'package:storetrack_app/core/utils/sync_utils.dart';
import 'package:storetrack_app/core/utils/photo_utils.dart';
import 'package:storetrack_app/features/home/<USER>/entities/upload_photo_response_entity.dart';
import 'package:storetrack_app/features/home/<USER>/entities/tasks_response_entity.dart';

/// Example usage of photo upload response handling
///
/// This file demonstrates how to use the new photo upload response handling
/// methods for duplicate detection and local storage optimization.
class PhotoUploadExample {
  /// Example: Handle photo upload response after API call
  ///
  /// This shows the typical workflow when processing photo upload responses:
  /// 1. Upload photo using existing API
  /// 2. Process the response with duplicate detection
  /// 3. Handle local storage optimization
  static Future<void> examplePhotoUploadWorkflow({
    required Photo photo,
    required int taskId,
    required int folderId,
  }) async {
    try {
      // Step 1: Create upload request (existing functionality)
      final uploadRequest = await SyncUtils.createUploadPhotoRequest(
        photo: photo,
        taskId: taskId,
        folderId: folderId,
      );

      // Step 2: Upload photo via API (this would be done in your repository/service)
      // final uploadResult = await homeRepository.uploadPhoto(uploadRequest);

      // Step 3: Handle the response (NEW FUNCTIONALITY)
      // if (uploadResult.isSuccess) {
      //   final uploadResponse = uploadResult.data!;
      //   final originalLocalPath = photo.localPath ?? '';
      //
      //   final success = await SyncUtils.handlePhotoUploadResponse(
      //     uploadResponse: uploadResponse,
      //     taskId: taskId,
      //     originalLocalPath: originalLocalPath,
      //   );
      //
      //   if (success) {
      //     print('Photo upload response handled successfully');
      //   } else {
      //     print('Failed to handle photo upload response');
      //   }
      // }
    } catch (e) {
      print('Error in photo upload workflow: $e');
    }
  }

  /// Example: Using the new uploadPhotoWithHandling method (RECOMMENDED)
  ///
  /// This shows the simplified workflow using the new repository method that
  /// automatically handles response processing, duplicate detection, and local storage optimization.
  static Future<void> exampleSimplifiedPhotoUploadWorkflow({
    required Photo photo,
    required int taskId,
    required int folderId,
  }) async {
    try {
      // Step 1: Create upload request
      final uploadRequest = await SyncUtils.createUploadPhotoRequest(
        photo: photo,
        taskId: taskId,
        folderId: folderId,
      );

      final originalLocalPath = photo.localPath ?? '';

      // Step 2: Upload photo with automatic response handling (NEW METHOD)
      // final uploadResult = await homeRepository.uploadPhotoWithHandling(
      //   request: uploadRequest,
      //   taskId: taskId,
      //   originalLocalPath: originalLocalPath,
      // );
      //
      // if (uploadResult.isSuccess) {
      //   print('Photo uploaded and processed successfully');
      //   // Duplicate detection and local storage optimization handled automatically
      // } else {
      //   print('Photo upload failed: ${uploadResult.errorMessage}');
      // }
    } catch (e) {
      print('Error in simplified photo upload workflow: $e');
    }
  }

  /// Example: Manual duplicate detection
  ///
  /// This shows how to manually check for duplicate photos
  static Future<void> exampleDuplicateDetection({
    required int taskId,
    required int photoId,
  }) async {
    final exists = await PhotoUtils.photoExists(
      taskId: taskId,
      photoId: photoId,
    );

    if (exists) {
      print('Photo with ID $photoId already exists in task $taskId');
    } else {
      print('Photo with ID $photoId does not exist in task $taskId');
    }
  }

  /// Example: Manual photo record deletion
  ///
  /// This shows how to manually delete photo records
  static Future<void> examplePhotoRecordDeletion({
    required int taskId,
    required String localPath,
  }) async {
    final success = await PhotoUtils.deletePhotoRecord(
      taskId: taskId,
      localPath: localPath,
      deleteLocalFile: true, // Also delete the local file
    );

    if (success) {
      print('Photo record deleted successfully');
    } else {
      print('Failed to delete photo record');
    }
  }

  /// Example: Manual photo record update with server data
  ///
  /// This shows how to manually update photo records with server data
  static Future<void> examplePhotoRecordUpdate({
    required int taskId,
    required String localPath,
    required int serverPhotoId,
    required String serverPhotoUrl,
    required String caption,
  }) async {
    final success = await PhotoUtils.updatePhotoWithServerData(
      taskId: taskId,
      localPath: localPath,
      photoId: serverPhotoId,
      photoUrl: serverPhotoUrl,
      caption: caption,
      cannotUploadMandatory: false,
      deleteLocalFile: true, // Delete local file to free up space
    );

    if (success) {
      print('Photo record updated with server data successfully');
    } else {
      print('Failed to update photo record with server data');
    }
  }

  /// Example: Find photo by local path
  ///
  /// This shows how to find a photo record by its local path
  static Future<void> exampleFindPhotoByLocalPath({
    required int taskId,
    required String localPath,
  }) async {
    final photo = await PhotoUtils.findPhotoByLocalPath(
      taskId: taskId,
      localPath: localPath,
    );

    if (photo != null) {
      print('Found photo: ID=${photo.photoId}, Caption=${photo.caption}');
    } else {
      print('Photo not found with local path: $localPath');
    }
  }

  /// Example: Complete photo upload and sync workflow
  ///
  /// This demonstrates the complete workflow for uploading multiple photos
  /// and handling their responses with proper duplicate detection and cleanup
  static Future<void> exampleCompletePhotoSyncWorkflow({
    required List<TaskDetail> tasks,
  }) async {
    try {
      // Step 1: Get all photos that need to be uploaded
      final photosToUpload = SyncUtils.getPhotosToUpload(tasks);
      print('Found ${photosToUpload.length} photos to upload');

      // Step 2: Upload each photo individually and handle responses
      for (final photoData in photosToUpload) {
        final photo = photoData['photo'] as Photo;
        final taskId = photoData['taskId'] as int;
        final folderId = photoData['folderId'] as int;
        final originalLocalPath = photo.localPath ?? '';

        if (originalLocalPath.isEmpty) {
          print('Skipping photo without local path: ${photo.photoId}');
          continue;
        }

        try {
          // Create upload request
          final uploadRequest = await SyncUtils.createUploadPhotoRequest(
            photo: photo,
            taskId: taskId,
            folderId: folderId,
          );

          // Upload photo (this would be done via your repository)
          // final uploadResult = await homeRepository.uploadPhoto(uploadRequest);

          // Handle response with duplicate detection and local storage optimization
          // if (uploadResult.isSuccess) {
          //   final success = await SyncUtils.handlePhotoUploadResponse(
          //     uploadResponse: uploadResult.data!,
          //     taskId: taskId,
          //     originalLocalPath: originalLocalPath,
          //   );
          //
          //   if (success) {
          //     print('Successfully processed photo upload response for: ${photo.photoId}');
          //   } else {
          //     print('Failed to process photo upload response for: ${photo.photoId}');
          //   }
          // }
        } catch (e) {
          print('Error uploading photo ${photo.photoId}: $e');
        }
      }

      // Step 3: After all individual uploads, sync photo metadata
      final syncRequest =
          await SyncUtils.createSyncPicInfoRequest(tasks: tasks);
      // final syncResult = await homeRepository.syncPhotoInfo(syncRequest);

      print('Photo upload and sync workflow completed');
    } catch (e) {
      print('Error in complete photo sync workflow: $e');
    }
  }
}
