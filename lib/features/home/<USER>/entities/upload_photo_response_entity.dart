import 'dart:convert';

import 'package:storetrack_app/features/home/<USER>/entities/tasks_response_entity.dart';

class UploadPhotoResponseEntity {
  final Photo data;

  const UploadPhotoResponseEntity({
    required this.data,
  });

  factory UploadPhotoResponseEntity.fromRawJson(String str) =>
      UploadPhotoResponseEntity.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory UploadPhotoResponseEntity.fromJson(Map<String, dynamic> json) =>
      UploadPhotoResponseEntity(
        data: Photo.fromJson(json["data"]),
      );

  Map<String, dynamic> toJson() => {
        "data": data.toJson(),
      };
}
