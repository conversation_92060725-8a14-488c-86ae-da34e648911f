import 'dart:convert';

import 'package:storetrack_app/features/home/<USER>/entities/tasks_response_entity.dart';

class UploadSignatureResponseEntity {
  final Signature data;

  const UploadSignatureResponseEntity({
    required this.data,
  });

  factory UploadSignatureResponseEntity.fromRawJson(String str) =>
      UploadSignatureResponseEntity.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory UploadSignatureResponseEntity.fromJson(Map<String, dynamic> json) =>
      UploadSignatureResponseEntity(
        data: Signature.fromJson(json["data"]),
      );

  Map<String, dynamic> toJson() => {
        "data": data.toJson(),
      };
}
